#!/usr/bin/env node

/**
 * Dashboard Performance Test
 * Tests that the teacher dashboard loads within 3 seconds
 */

const puppeteer = require('puppeteer');

async function testDashboardPerformance() {
  console.log('🚀 Starting Dashboard Performance Test...');
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set viewport for consistent testing
    await page.setViewport({ width: 1920, height: 1080 });
    
    // Start timing
    const startTime = Date.now();
    
    // Navigate to dashboard
    console.log('📊 Loading teacher dashboard...');
    await page.goto('http://localhost:8081/teacher/dashboard', {
      waitUntil: 'networkidle0',
      timeout: 10000 // 10 second timeout
    });
    
    // Calculate load time
    const loadTime = Date.now() - startTime;
    const loadTimeSeconds = loadTime / 1000;
    
    console.log(`⏱️  Dashboard load time: ${loadTimeSeconds.toFixed(2)} seconds`);
    
    // Check if load time is under 3 seconds
    if (loadTimeSeconds <= 3) {
      console.log('✅ SUCCESS: Dashboard loads within 3 seconds');
      return true;
    } else {
      console.log('❌ FAILURE: Dashboard takes longer than 3 seconds to load');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Error during performance test:', error.message);
    return false;
  } finally {
    await browser.close();
  }
}

// Run the test
testDashboardPerformance()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
